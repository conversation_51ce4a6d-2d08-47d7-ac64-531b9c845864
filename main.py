import os
import re
from collections import defaultdict

import datetime
import requests

# 新增：发送飞书消息的方法
def send_feishu_notice(title, msg_lines, platform):
    ios_notice_url = "https://open.feishu.cn/open-apis/bot/v2/hook/f2e6c6bd-c29c-4c5e-8efd-9e0492924a9c"
    android_notice_url = "https://open.feishu.cn/open-apis/bot/v2/hook/ec4089cb-cbf6-44cb-a98c-92976dd91ee8"
    apk_notice_url = "https://open.feishu.cn/open-apis/bot/v2/hook/d3012965-eb4d-4534-9360-4e5416361d88"

    notice_url = ""
    if platform == 1:
        notice_url = ios_notice_url
    if platform == 2:
        notice_url = android_notice_url
    if platform == 3:
        notice_url = apk_notice_url


    body_json = {
        "msg_type": "post",
        "content": {
            "post": {
                "zh_cn": {
                    "title": title,
                    "content": []
                }
            }
        }
    }

    for msg_line in msg_lines:
        msg_line_ja = [
            {
                "tag": "text",
                "text": msg_line
            }
        ]
        body_json["content"]["post"]["zh_cn"]["content"].append(msg_line_ja)

    response = requests.post(notice_url, json=body_json)
    if response.status_code == 200:
        print("飞书消息发送成功")
    else:
        print(f"飞书消息发送失败，状态码: {response.status_code}")

# 新增：统计总用户量
def count_total_users(log_file_path, platform):
    user_ids = set()

    with open(log_file_path, 'r') as file:
        for line in file:
            if 'sync dau event' in line and (f'platformType:{platform}' in line or f'platform: {platform}' in line):
                match = re.search(r'userId: (\d+)', line)
                if match:
                    user_ids.add(match.group(1))

    return len(user_ids)

# 新增：统计新增用户量
def count_new_users(log_file_path, platform):
    new_users = set()

    with open(log_file_path, 'r') as file:
        for line in file:
            if 'sync dnu event' in line and (f'platformType:{platform}' in line or f'platform: {platform}' in line):
                match = re.search(r'userId: (\d+)', line)
                if match:
                    new_users.add(match.group(1))

    return len(new_users)

# 统计用户使用图片换脸的功能
def count_users_using_face_swap(log_file_path, use_count, platform):
    return count_users_with_count(log_file_path, use_count, '使用图片换脸', platform)

# 统计用户请求匹配的功能
def count_users_request_match(log_file_path, use_count, platform):
    return count_users_with_count(log_file_path, use_count, '用户请求匹配', platform)

# 统计用户聊天的功能
def count_use_chat(log_file_path, platform):
    return count_users_with_count(log_file_path, 1,'create chat session', platform)

# 统计用户聊天的功能
def count_users_chat(log_file_path, use_count, platform):
    return count_users_with_count(log_file_path, use_count, 'send msg', platform)

# 统计用户使用换脸
def count_users_using_video_swap(log_file_path, use_count, platform):
    return count_users_with_count(log_file_path, use_count, '使用视频换脸', platform)

# 新增：统计使用文生图功能的用户数量
def count_users_using_text_to_image(log_file_path, use_count, platform):
    return count_users_with_count(log_file_path, use_count, '使用文生图', platform)

# 新增：统计使用文生视频功能的用户数量
def count_users_using_text_to_video(log_file_path, use_count, platform):
    return count_users_with_count(log_file_path, use_count, '使用文生视频', platform)

# 统计用户使用图生视频
def count_users_using_image_to_video(log_file_path, use_count, platform):
    return count_users_with_count(log_file_path, use_count, '使用图生视频', platform)

# 统计用户使用AiKiss
def count_users_using_ai_kiss(log_file_path, use_count, platform):
    return count_users_with_count(log_file_path, use_count, '使用AiKiss', platform)

# 统计用户使用AiHug
def count_users_using_ai_hug(log_file_path, use_count, platform):
    return count_users_with_count(log_file_path, use_count, '使用AiHug', platform)

# 新增：统计新增订阅数量
def count_new_subscriptions(log_file_path, platform):
    subscription_count = 0

    with open(log_file_path, 'r') as file:
        for line in file:
            if '新增订阅' in line and (f'platformType:{platform}' in line or f'platformType: {platform}' in line or f'platform: {platform}' in line):
                subscription_count += 1

    return subscription_count

# 新增：统计聊最多的用户及其聊天次数
def find_most_chatty_user(log_file_path, platform):
    new_users = set()
    old_users = set()

    with open(log_file_path, 'r') as file:
        for line in file:
            if 'sync dnu event' in line and (f'platformType:{platform}' in line or f'platform: {platform}' in line):
                match = re.search(r'userId: (\d+)', line)
                if match:
                    new_users.add(match.group(1))

    user_chat_count = defaultdict(int)

    with open(log_file_path, 'r') as file:
        for line in file:
            if 'create chat session' in line and (f'platformType:{platform}' in line or f'platform: {platform}' in line):
                match = re.search(r'userId:(\d+)', line)
                if match:
                    user_id = match.group(1)
                    user_chat_count[user_id] += 1

    new_user_chat_count = {user_id: count for user_id, count in user_chat_count.items() if user_id in new_users}
    old_user_chat_count = {user_id: count for user_id, count in user_chat_count.items() if user_id not in new_users}

    if new_user_chat_count:
        most_chatty_new_user_id = max(new_user_chat_count, key=new_user_chat_count.get)
        most_chatty_new_user_count = new_user_chat_count[most_chatty_new_user_id]
    else:
        most_chatty_new_user_id, most_chatty_new_user_count = None, 0

    if old_user_chat_count:
        most_chatty_old_user_id = max(old_user_chat_count, key=old_user_chat_count.get)
        most_chatty_old_user_count = old_user_chat_count[most_chatty_old_user_id]
    else:
        most_chatty_old_user_id, most_chatty_old_user_count = None, 0

    return (most_chatty_new_user_id, most_chatty_new_user_count), (most_chatty_old_user_id, most_chatty_old_user_count)

# 统计用户使用功能，并统计次数
def count_users_with_count(log_file_path, chat_count_threshold, match_text, platform):
    all_new_users = set()
    user_chat_count = defaultdict(int)

    # 第一次遍历日志文件，提取所有新用户
    with open(log_file_path, 'r') as file:
        for line in file:
            if 'sync dnu event' in line and (f'platformType:{platform}' in line or f'platformType: {platform}' in line or f'platform: {platform}' in line):
                match = re.search(r'userId: (\d+)', line)
                if match:
                    all_new_users.add(match.group(1))

    # 第二次遍历日志文件，统计包含 match_text 的用户使用次数
    with open(log_file_path, 'r') as file:
        for line in file:
            if match_text in line and (f'platformType:{platform}' in line or f'platformType: {platform}' in line or f'platform: {platform}' in line):
                match = re.search(r'userId:(\d+)', line)
                if match:
                    user_id = match.group(1)
                    user_chat_count[user_id] += 1

    # 统计新用户和老用户的使用数量，并打印符合条件的用户ID
    new_users_ids = []
    old_users_ids = []

    new_use_user_count = 0
    old_user_count = 0

    for user_id, count in user_chat_count.items():
        if count >= chat_count_threshold:
            if user_id in all_new_users:
                new_use_user_count += 1
                new_users_ids.append(user_id)
            else:
                old_user_count += 1
                old_users_ids.append(user_id)

    # 打印满足条件的用户ID
    print("新用户满足条件的用户ID:", new_users_ids)
    print("老用户满足条件的用户ID:", old_users_ids)


    # 统计新用户和老用户的使用数量
    new_use_user_count = sum(1 for user_id, count in user_chat_count.items() if count >= chat_count_threshold and user_id in all_new_users)
    old_user_count = sum(1 for user_id, count in user_chat_count.items() if count >= chat_count_threshold and user_id not in all_new_users)
    return new_use_user_count, old_user_count

# 新增：获取日期最大的日志文件路径
def get_latest_log_file(log_file_paths):
    """
    从日志文件路径列表中获取日期最大的文件路径。
    假设文件名格式为 'client.YYYY-MM-DD.HH.log'。
    """
    latest_file = None
    latest_date = None

    for file_path in log_file_paths:
        # 提取文件名中的日期部分
        file_name = file_path.split('/')[-1]  # 获取文件名
        date_str = file_name.split('.')[1]    # 提取日期部分
        try:
            file_date = datetime.datetime.strptime(date_str, "%Y-%m-%d").date()
            if latest_date is None or file_date > latest_date:
                latest_date = file_date
                latest_file = file_path
        except ValueError:
            print(f"无法解析文件名中的日期: {file_name}")

    return latest_file

# 处理日志文件
def process_log_file(base_logo_path, platform):
    # 获取所有日志文件路径
    log_file_paths = []

    # 遍历指定目录中的文件
    for root, dirs, files in os.walk(base_logo_path):
        for file in files:
            # 获取每个文件的完整路径
            file_path = os.path.join(root, file)
            log_file_paths.append(file_path)

    # 获取日期最大的日志文件路径
    log_file_path = get_latest_log_file(log_file_paths)
    date_match = re.search(r'\d{4}-\d{2}-\d{2}', log_file_path)
    date_str = date_match.group(0)

    if log_file_path is None:
        print("未找到有效的日志文件")
        exit(1)

    print(f"使用日志文件: {log_file_path}")

    msg_lines = []
    msg_title = f"【{date_str}】数据统计"

    # 新增：统计今日总用户量
    total_user_count = count_total_users(log_file_path, platform)
    # # 新增：统计今日新用户量
    new_user_count = count_new_users(log_file_path, platform)
    msg_lines.append(f"今日活跃: {total_user_count}, 今日新增: {new_user_count}")

    # 新增：统计新增订阅数量
    subscription_count = count_new_subscriptions(log_file_path, platform)
    msg_lines.append(f"新增订阅: {subscription_count}")

    # 统计用户请求匹配的新用户和老用户数量
    request_match_new_user_count, request_match_old_user_count = count_users_request_match(log_file_path, 1, platform)
    msg_lines.append(f"使用随机匹配: 新用户{request_match_new_user_count}人, 老用户{request_match_old_user_count}人")

    # 统计使用文生图功能的新用户和老用户数量
    text_to_image_new_user_count, text_to_image_old_user_count = count_users_using_text_to_image(log_file_path, 1, platform)
    msg_lines.append(f"使用文生图: 新用户{text_to_image_new_user_count}人, 老用户{text_to_image_old_user_count}人")

    # 统计使用文生视频功能的新用户和老用户数量
    text_to_image_new_user_count, text_to_image_old_user_count = count_users_using_text_to_video(log_file_path, 1, platform)
    msg_lines.append(f"使用文生视频: 新用户{text_to_image_new_user_count}人, 老用户{text_to_image_old_user_count}人")

    # 统计使用图生视频功能的新用户和老用户数量
    text_to_image_new_user_count, text_to_image_old_user_count = count_users_using_image_to_video(log_file_path, 1, platform)
    msg_lines.append(f"使用图生视频: 新用户{text_to_image_new_user_count}人, 老用户{text_to_image_old_user_count}人")

    # 统计使用AiKiss功能的新用户和老用户数量
    text_to_image_new_user_count, text_to_image_old_user_count = count_users_using_ai_kiss(log_file_path, 1, platform)
    msg_lines.append(f"使用AiKiss: 新用户{text_to_image_new_user_count}人, 老用户{text_to_image_old_user_count}人")

    # 统计使用AiHug功能的新用户和老用户数量
    text_to_image_new_user_count, text_to_image_old_user_count = count_users_using_ai_hug(log_file_path, 1, platform)
    msg_lines.append(f"使用AiHug: 新用户{text_to_image_new_user_count}人, 老用户{text_to_image_old_user_count}人")

    # 统计使用图片换脸的新用户和老用户数量
    face_swap_new_user_count, face_swap_old_user_count = count_users_using_face_swap(log_file_path, 1, platform)
    msg_lines.append(f"使用图片换脸: 新用户{face_swap_new_user_count}人, 老用户{face_swap_old_user_count}人")
    face_swap_2_times_new_user_count, face_swap_2_times_old_user_count = count_users_using_face_swap(log_file_path, 2, platform)
    msg_lines.append(f"使用图片换脸2次: 新用户{face_swap_2_times_new_user_count}人, 老用户{face_swap_2_times_old_user_count}人")
    # 新增：统计使用图片换脸达到3次的新用户数量
    face_swap_3_times_new_user_count, face_swap_3_times_old_user_count = count_users_using_face_swap(log_file_path, 3, platform)
    msg_lines.append(f"使用图片换脸3次: 新用户{face_swap_3_times_new_user_count}人, 老用户{face_swap_3_times_old_user_count}人")
    # 新增：统计使用图片换脸达到4次的新用户数量
    face_swap_4_times_new_user_count, face_swap_4_times_old_user_count = count_users_using_face_swap(log_file_path, 4, platform)
    msg_lines.append(f"使用图片换脸4次: 新用户{face_swap_4_times_new_user_count}人, 老用户{face_swap_4_times_old_user_count}人")
    # 新增：统计使用图片换脸达到5次的新用户数量
    face_swap_5_times_new_user_count, face_swap_5_times_old_user_count = count_users_using_face_swap(log_file_path, 5, platform)
    msg_lines.append(f"使用图片换脸5次: 新用户{face_swap_5_times_new_user_count}人, 老用户{face_swap_5_times_old_user_count}人")

    # 统计使用视频换脸的新用户和老用户数量
    video_swap_new_user_count, video_swap_old_user_count = count_users_using_video_swap(log_file_path, 1, platform)
    msg_lines.append(f"使用视频换脸: 新用户{video_swap_new_user_count}人, 老用户{video_swap_old_user_count}人")
    # 新增：统计使用视频换脸达到2次的新用户数量
    video_swap_2_times_new_user_count, video_swap_2_times_old_user_count = count_users_using_video_swap(log_file_path,2, platform)
    msg_lines.append(f"使用视频换脸2次: 新用户{video_swap_2_times_new_user_count}人, 老用户{video_swap_2_times_old_user_count}人")
    # 新增：统计使用视频换脸达到3次的新用户数量
    video_swap_3_times_new_user_count, video_swap_3_times_old_user_count = count_users_using_video_swap(log_file_path,3, platform)
    msg_lines.append(f"使用视频换脸3次: 新用户{video_swap_3_times_new_user_count}人, 老用户{video_swap_3_times_old_user_count}人")
    # 新增：统计使用视频换脸达到4次的新用户数量
    video_swap_4_times_new_user_count, video_swap_4_times_old_user_count = count_users_using_video_swap(log_file_path,4, platform)
    msg_lines.append(f"使用视频换脸4次: 新用户{video_swap_4_times_new_user_count}人, 老用户{video_swap_4_times_old_user_count}人")
    # 新增：统计使用视频换脸达到5次的新用户数量
    video_swap_5_times_new_user_count, video_swap_5_times_old_user_count = count_users_using_video_swap(log_file_path,5, platform)
    msg_lines.append(f"使用视频换脸5次: 新用户{video_swap_5_times_new_user_count}人, 老用户{video_swap_5_times_old_user_count}人")

    # 统计创建聊天会话的新用户和老用户数量
    create_chat_session_new_user_count, create_chat_session_old_user_count = count_use_chat(log_file_path, platform)
    msg_lines.append(f"使用聊天: 新用户{create_chat_session_new_user_count}人, 老用户{create_chat_session_old_user_count}人")
    # 统计聊天次数达到阈值的新用户和老用户数量
    chat_2_times_new_user_count, chat_2_times_old_user_count = count_users_chat(log_file_path, 2, platform)
    msg_lines.append(f"聊天次数2次: 新用户{chat_2_times_new_user_count}人, 老用户{chat_2_times_old_user_count}人")
    chat_3_times_new_user_count, chat_3_times_old_user_count = count_users_chat(log_file_path, 3, platform)
    msg_lines.append(f"聊天次数3次: 新用户{chat_3_times_new_user_count}人, 老用户{chat_3_times_old_user_count}人")
    chat_4_times_new_user_count, chat_4_times_old_user_count = count_users_chat(log_file_path, 4, platform)
    msg_lines.append(f"聊天次数4次: 新用户{chat_4_times_new_user_count}人, 老用户{chat_4_times_old_user_count}人")
    chat_5_times_new_user_count, chat_5_times_old_user_count = count_users_chat(log_file_path, 5, platform)
    msg_lines.append(f"聊天次数5次: 新用户{chat_5_times_new_user_count}人, 老用户{chat_5_times_old_user_count}人")
    chat_10_times_new_user_count, chat_10_times_old_user_count = count_users_chat(log_file_path, 10, platform)
    msg_lines.append(f"聊天次数10次: 新用户{chat_10_times_new_user_count}人, 老用户{chat_10_times_old_user_count}人")
    chat_15_times_new_user_count, chat_15_times_old_user_count = count_users_chat(log_file_path, 15, platform)
    msg_lines.append(f"聊天次数15次: 新用户{chat_15_times_new_user_count}人, 老用户{chat_15_times_old_user_count}人")

    # 调用飞书消息发送方法
    print(msg_lines)
    # send_feishu_notice(msg_title, msg_lines, platform)


if __name__ == "__main__":
    # base_log_path = '/data/app/client/logs/'
    base_log_path = '/Users/<USER>/cidata/aichatter/source/'

    # process_log_file(base_log_path, 1)
    # process_log_file(base_log_path, 2)
    process_log_file(base_log_path, 2)


